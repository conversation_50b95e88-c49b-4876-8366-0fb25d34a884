# Kylas AI Smart Assistant

A comprehensive AI-powered assistant platform that provides intelligent automation features for CRM operations including email generation, call analysis, note analysis, smart list filtering, and WhatsApp template creation.

## 🚀 Features

### 1. **Email Intelligence**
- **Smart Email Composition**: Generate professional emails using AI based on user prompts
- **Intelligent Reply Generation**: Automatically generate contextual email replies based on conversation history
- **Email Rewriting**: Rewrite existing emails with specific instructions for tone, style, or content improvements
- **Context-Aware Responses**: Leverages previous email conversations for better context understanding

### 2. **Call Analysis & Insights**
- **Audio Transcription**: Convert call recordings to text using advanced speech-to-text technology
- **Sentiment Analysis**: Analyze customer emotions and sentiment during calls
- **Call Summarization**: Generate concise summaries of call conversations
- **Action Item Extraction**: Automatically identify and extract action items from call transcripts
- **Task Creation**: Create follow-up tasks based on call analysis results

### 3. **Note Analysis & Processing**
- **Intelligent Note Processing**: Analyze meeting notes and extract meaningful insights
- **Action Item Detection**: Automatically identify action items from notes with priority levels
- **Task Generation**: Create tasks with due dates based on note analysis
- **Priority Assignment**: Assign priority levels to extracted action items

### 4. **Smart List & Filtering**
- **Natural Language Queries**: Convert natural language queries into structured database filters
- **Dynamic Filter Generation**: Generate JSON-based filter rules for CRM data
- **Multi-Entity Support**: Support for leads, contacts, companies, and deals
- **Vector-Based Search**: Utilize Elasticsearch for intelligent field matching and suggestions

### 5. **WhatsApp Template Management**
- **AI-Powered Template Creation**: Generate WhatsApp business templates using AI
- **Template Rewriting**: Modify existing templates with specific instructions
- **Permission-Based Access**: Role-based access control for template management
- **Multi-Language Support**: Support for various languages and locales

## 🏗️ Architecture

### Technology Stack

**Backend Framework:**
- **FastAPI**: Modern, fast web framework for building APIs
- **Python 3.9+**: Core programming language
- **Uvicorn**: ASGI server for production deployment

**Database & Storage:**
- **PostgreSQL**: Primary relational database
- **SQLAlchemy**: ORM for database operations
- **Alembic**: Database migration management
- **Elasticsearch**: Vector database for intelligent search
- **Redis**: Caching and rate limiting

**AI & Machine Learning:**
- **OpenAI GPT**: Primary language model for text generation
- **Google Gemini**: Alternative AI model for specific use cases
- **Groq**: High-performance AI inference
- **Tiktoken**: Token counting and management

**Message Queue & Communication:**
- **RabbitMQ**: Asynchronous message processing
- **aio-pika**: Async RabbitMQ client

**Monitoring & Observability:**
- **Prometheus**: Metrics collection and monitoring
- **Loguru**: Structured logging
- **Health Checks**: Kubernetes-ready health endpoints

**Security & Authentication:**
- **JWT**: Token-based authentication
- **OAuth2**: Secure API access
- **Rate Limiting**: Request throttling and abuse prevention

### System Components

1. **API Layer**: RESTful APIs for all features
2. **Service Layer**: Business logic and AI integration
3. **Data Layer**: Database models and repositories
4. **Message Queue**: Asynchronous processing
5. **Vector Database**: Intelligent search and matching
6. **Monitoring**: Metrics and health monitoring

## 📊 System Flow Diagram

The system follows a microservices architecture with clear separation of concerns:

```mermaid
graph TB
    %% External Systems
    Client[Client Application]
    IAM[IAM Service]
    EmailService[Email Service]

    %% API Gateway
    API[FastAPI Application]

    %% Core Services
    EmailGen[Email Generation Service]
    CallAnalysis[Call Analysis Service]
    NoteAnalysis[Note Analysis Service]
    SmartList[Smart List Service]
    WhatsApp[WhatsApp Template Service]

    %% AI Models
    OpenAI[OpenAI GPT]
    Gemini[Google Gemini]
    Groq[Groq AI]

    %% Data Storage
    PostgreSQL[(PostgreSQL Database)]
    Elasticsearch[(Elasticsearch)]
    Redis[(Redis Cache)]

    %% Message Queue
    RabbitMQ[RabbitMQ]

    %% Monitoring
    Prometheus[Prometheus Metrics]
    HealthCheck[Health Check]

    %% Client Interactions
    Client -->|HTTP Requests| API
    API -->|JWT Validation| IAM
    API -->|Email Data| EmailService

    %% API Routes
    API --> EmailGen
    API --> CallAnalysis
    API --> NoteAnalysis
    API --> SmartList
    API --> WhatsApp

    %% Service to AI Models
    EmailGen --> OpenAI
    CallAnalysis --> Gemini
    NoteAnalysis --> OpenAI
    SmartList --> OpenAI
    WhatsApp --> OpenAI

    %% Alternative AI Models
    EmailGen -.-> Groq
    CallAnalysis -.-> OpenAI

    %% Data Storage Connections
    EmailGen --> PostgreSQL
    CallAnalysis --> PostgreSQL
    NoteAnalysis --> PostgreSQL
    SmartList --> PostgreSQL
    WhatsApp --> PostgreSQL

    %% Vector Search
    SmartList --> Elasticsearch

    %% Caching
    API --> Redis
    EmailGen --> Redis
    SmartList --> Redis

    %% Message Queue Processing
    API --> RabbitMQ
    RabbitMQ --> CallAnalysis
    RabbitMQ --> NoteAnalysis
    RabbitMQ --> SmartList

    %% Monitoring
    API --> Prometheus
    API --> HealthCheck

    %% Styling
    classDef client fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef service fill:#e8f5e8
    classDef ai fill:#fff3e0
    classDef storage fill:#fce4ec
    classDef queue fill:#f1f8e9
    classDef monitor fill:#e0f2f1

    class Client,IAM,EmailService client
    class API api
    class EmailGen,CallAnalysis,NoteAnalysis,SmartList,WhatsApp service
    class OpenAI,Gemini,Groq ai
    class PostgreSQL,Elasticsearch,Redis storage
    class RabbitMQ queue
    class Prometheus,HealthCheck monitor
```

## 🔄 Sequence Diagrams

### Email Generation Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as FastAPI
    participant Auth as JWT Validator
    participant RateLimit as Rate Limiter
    participant EmailService as Email Service
    participant OpenAI
    participant DB as PostgreSQL
    participant Redis

    Client->>API: POST /v1/smart-assistant/emails/compose
    Note over Client,API: Request with content, subject, etc.

    API->>Auth: Validate JWT Token
    Auth-->>API: tenant_id, user_id, username

    API->>RateLimit: Check rate limits
    RateLimit->>Redis: Get current usage
    Redis-->>RateLimit: Usage data
    RateLimit-->>API: Rate limit status

    API->>EmailService: Generate email request
    Note over EmailService: Process user content and context

    EmailService->>OpenAI: Generate email content
    Note over OpenAI: AI processes prompt and generates email
    OpenAI-->>EmailService: Generated email (subject + body)

    EmailService->>DB: Save request history
    Note over DB: Store input, output, tokens, metadata
    DB-->>EmailService: Request ID

    EmailService->>DB: Update token usage
    DB-->>EmailService: Updated usage

    EmailService-->>API: Email response with ID
    API-->>Client: Generated email content

    Note over Client: User can accept/reject the email

    opt User accepts email
        Client->>API: POST /v1/smart-assistant/emails/{id}/accepted
        API->>EmailService: Mark as accepted
        EmailService->>DB: Update acceptance status
        DB-->>EmailService: Success
        EmailService-->>API: Success response
        API-->>Client: Acceptance confirmed
    end
```

### Call Analysis Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as FastAPI
    participant Auth as JWT Validator
    participant CallService as Call Analysis Service
    participant RabbitMQ
    participant AudioService as Audio Processing
    participant Gemini as Google Gemini
    participant TaskService as Task Creation
    participant DB as PostgreSQL
    participant ExternalAPI as External CRM API

    Client->>API: POST /v1/smart-assistant/call-analysis
    Note over Client,API: Request with callId

    API->>Auth: Validate JWT Token
    Auth-->>API: tenant_id, user_id, username

    API->>CallService: Process call analysis request
    CallService->>RabbitMQ: Publish call analysis message
    Note over RabbitMQ: Async processing queue

    API-->>Client: Analysis accepted (202)
    Note over Client: Client receives immediate response

    RabbitMQ->>CallService: Consume call analysis message

    CallService->>ExternalAPI: Get call recording URL
    ExternalAPI-->>CallService: Recording URL

    CallService->>AudioService: Download audio file
    AudioService-->>CallService: Audio file

    CallService->>AudioService: Transcribe audio
    AudioService-->>CallService: Transcript text

    CallService->>Gemini: Analyze call content
    Note over Gemini: Extract summary, sentiment, emotions, action items
    Gemini-->>CallService: Analysis results

    CallService->>DB: Save analysis results
    DB-->>CallService: Saved successfully

    opt Create tasks from action items
        CallService->>TaskService: Get task form fields
        TaskService->>ExternalAPI: Fetch priority values and status
        ExternalAPI-->>TaskService: Form field data
        TaskService-->>CallService: Task form fields

        loop For each action item
            CallService->>TaskService: Create task
            TaskService->>ExternalAPI: Create task in CRM
            ExternalAPI-->>TaskService: Task created
            TaskService-->>CallService: Task creation result
        end
    end

    CallService->>RabbitMQ: Publish analysis complete
    Note over RabbitMQ: Notify completion to other services
```

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.9 or higher
- PostgreSQL 12+
- Redis 6+
- RabbitMQ 3.8+
- Elasticsearch 8.x
- Docker (optional, for containerized deployment)

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# General Settings
ENVIRONMENT=development
SERVER_PORT=8000
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-db-password
POSTGRES_DB=kylas_ai
DATABASE_URL=postgresql://user:password@localhost:5432/kylas_ai

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VIRTUAL_HOST=/

# External Services
IAM_BASE_PATH=https://your-iam-service.com
EMAIL_BASE_PATH=https://your-email-service.com

# AI Model API Keys
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
GROQ_API_KEY=your-groq-api-key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
```

### Local Development Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd sd-ai
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up the database:**
   ```bash
   # Run database migrations
   alembic upgrade head
   ```

5. **Start the development server:**
   ```bash
   python start_server.py
   ```

### Docker Deployment

1. **Build the Docker image:**
   ```bash
   docker build -t kylas-ai .
   ```

2. **Run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

### Production Deployment

The application is designed for Kubernetes deployment with:
- Health check endpoints (`/health/liveness`, `/health/readiness`)
- Prometheus metrics endpoint (`/metrics`)
- Graceful shutdown handling
- Multi-worker support with Uvicorn

## 📚 API Documentation

### Base URL
```
https://your-domain.com/v1/smart-assistant
```

### Authentication
All API endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <your-jwt-token>
```

### Email APIs

#### Generate New Email
```http
POST /emails/compose
Content-Type: application/json

{
  "content": "Write a follow-up email to discuss the project timeline",
  "subject": "Project Timeline Discussion",
  "tone": "professional",
  "length": "medium"
}
```

**Response:**
```json
{
  "content": {
    "id": 12345,
    "subject": "Project Timeline Discussion",
    "content": "Dear [Recipient],\n\nI hope this email finds you well..."
  }
}
```

#### Generate Email Reply
```http
POST /emails/reply
Content-Type: application/json

{
  "emailId": 67890,
  "content": "Agree to the proposed timeline and suggest a meeting",
  "tone": "friendly"
}
```

#### Rewrite Email
```http
POST /emails/{emailId}/rewrite
Content-Type: application/json

{
  "instruction": "Make it more formal and add a call to action"
}
```

#### Accept Email Response
```http
POST /emails/{requestId}/accepted
```

### Call Analysis APIs

#### Analyze Call
```http
POST /call-analysis
Content-Type: application/json

{
  "callId": 12345
}
```

**Response:**
```json
{
  "message": "Call analysis request accepted",
  "callId": 12345
}
```

#### Get Task Form Fields
```http
GET /call-analysis/task-form-fields?tenantId=123
```

### Smart List APIs

#### Generate Filters
```http
POST /
Content-Type: application/json

{
  "userQuery": "Show me all leads from California with high priority"
}
```

**Response:**
```json
{
  "jsonRule": {
    "rules": [
      {
        "operator": "equal",
        "field": "state",
        "value": "California"
      },
      {
        "operator": "equal",
        "field": "priority",
        "value": "High"
      }
    ],
    "condition": "and",
    "valid": true
  },
  "entityType": "lead",
  "operation": "search"
}
```

### WhatsApp Template APIs

#### Generate Template
```http
POST /whatsapp-templates/compose
Content-Type: application/json

{
  "content": "Create a welcome message for new customers",
  "templateType": "welcome",
  "language": "en"
}
```

#### Rewrite Template
```http
POST /whatsapp-templates/{templateId}/rewrite
Content-Type: application/json

{
  "instruction": "Make it more engaging and add emojis"
}
```

#### Accept Template
```http
POST /whatsapp-templates/{templateId}/accepted
```

### Health Check APIs

#### Liveness Probe
```http
GET /health/liveness
```

#### Readiness Probe
```http
GET /health/readiness
```

### Metrics

#### Prometheus Metrics
```http
GET /metrics
```

## 🗄️ Database Schema

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    name VARCHAR(512) NOT NULL,
    email VARCHAR(255),
    tenant_id BIGINT NOT NULL
);
```

#### Requests History Table
```sql
CREATE TABLE requests_history (
    id BIGINT PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    tenant_id BIGINT NOT NULL,
    requested_input JSONB NOT NULL,
    generated_response JSONB NOT NULL,
    request_date TIMESTAMP NOT NULL,
    email_id BIGINT,
    input_token_consumed BIGINT NOT NULL,
    output_token_consumed BIGINT NOT NULL,
    total_consumed BIGINT NOT NULL,
    response_accepted_status BOOLEAN NOT NULL,
    entity_id BIGINT,
    entity_type VARCHAR,
    operation VARCHAR,
    status VARCHAR DEFAULT 'COMPLETED'
);
```

#### Token History Table
```sql
CREATE TABLE token_history (
    id BIGINT PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    tenant_id BIGINT NOT NULL,
    token_limit BIGINT NOT NULL,
    token_consumed BIGINT NOT NULL,
    usage_date DATE NOT NULL DEFAULT CURRENT_DATE
);
```

## 🔧 Configuration

### Rate Limiting
- Configurable per-tenant rate limits
- Redis-based rate limiting
- Token-based usage tracking
- Graceful degradation on limit exceeded

### AI Model Configuration
- Primary: OpenAI GPT models
- Fallback: Google Gemini, Groq
- Configurable model parameters
- Token usage optimization

### Message Queue Configuration
- RabbitMQ for asynchronous processing
- Separate queues for different services
- Dead letter queues for error handling
- Auto-restart mechanisms for consumers

## 📊 Monitoring & Observability

### Metrics
- **Prometheus Integration**: Custom metrics for API performance
- **Request Tracking**: Response times, error rates, throughput
- **AI Model Metrics**: Token usage, model performance, costs
- **System Metrics**: Database connections, queue depths, cache hit rates

### Logging
- **Structured Logging**: JSON-formatted logs with Loguru
- **Request Tracing**: Unique request IDs for tracking
- **Error Tracking**: Detailed error logs with context
- **Performance Logging**: Slow query detection, bottleneck identification

### Health Checks
- **Liveness Probe**: Basic application health
- **Readiness Probe**: Database and external service connectivity
- **Dependency Checks**: AI model availability, queue connectivity

## 🚀 Performance Optimization

### Caching Strategy
- **Redis Caching**: Frequently accessed data
- **Response Caching**: AI model responses for similar queries
- **Rate Limit Caching**: User quota and usage tracking

### Asynchronous Processing
- **Background Jobs**: Long-running AI analysis tasks
- **Queue Management**: Priority-based task processing
- **Batch Processing**: Bulk operations for efficiency

### Database Optimization
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Indexed queries and efficient joins
- **Migration Management**: Alembic for schema versioning

## 🔒 Security Features

### Authentication & Authorization
- **JWT-based Authentication**: Secure token-based access
- **Role-based Access Control**: Permission-based feature access
- **Multi-tenant Architecture**: Isolated data per organization
- **Token Validation**: Real-time token verification with IAM service

### Data Protection
- **Input Sanitization**: Protection against injection attacks
- **Rate Limiting**: DDoS and abuse prevention
- **Audit Logging**: Complete request/response tracking
- **Encryption**: Secure data transmission and storage

### API Security
- **CORS Configuration**: Cross-origin request management
- **Request Validation**: Pydantic-based input validation
- **Error Handling**: Secure error responses without data leakage
- **Health Check Security**: Protected monitoring endpoints

## 🧪 Testing

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest test/unit/test_email_service.py

# Run with verbose output
pytest -v
```

### Test Structure
```
test/
├── unit/
│   ├── test_email_service.py
│   ├── test_call_analysis.py
│   ├── test_smart_list.py
│   └── test_auth.py
├── integration/
│   ├── test_api_endpoints.py
│   └── test_database.py
└── fixtures/
    ├── sample_data.py
    └── mock_responses.py
```

## 🚀 Deployment

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kylas-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kylas-ai
  template:
    metadata:
      labels:
        app: kylas-ai
    spec:
      containers:
      - name: kylas-ai
        image: kylas-ai:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: kylas-ai-secrets
              key: database-url
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### CI/CD Pipeline
The project includes Jenkins pipeline configuration for:
- **Automated Testing**: Unit and integration tests
- **Code Quality**: Linting and security scanning
- **Docker Build**: Multi-stage container builds
- **Deployment**: Automated deployment to staging/production
- **Monitoring**: Post-deployment health checks

## 📈 Scaling Considerations

### Horizontal Scaling
- **Stateless Design**: No server-side session storage
- **Load Balancing**: Multiple application instances
- **Database Scaling**: Read replicas and connection pooling
- **Cache Scaling**: Redis clustering for high availability

### Performance Tuning
- **Worker Configuration**: Optimal Uvicorn worker count
- **Memory Management**: Efficient resource utilization
- **AI Model Optimization**: Response caching and batching
- **Database Indexing**: Query performance optimization

## 🤝 Contributing

### Development Guidelines
1. **Code Style**: Follow PEP 8 and use Black formatter
2. **Testing**: Write tests for all new features
3. **Documentation**: Update API documentation for changes
4. **Security**: Follow security best practices
5. **Performance**: Consider performance impact of changes

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Write tests for your changes
4. Ensure all tests pass
5. Update documentation
6. Submit pull request with detailed description

## 📞 Support & Contact

### Documentation
- **API Documentation**: Available at `/v2/api-docs` when running
- **OpenAPI Spec**: Interactive API documentation
- **Health Monitoring**: Real-time system status

### Troubleshooting
- **Logs**: Check application logs for detailed error information
- **Health Checks**: Use health endpoints to verify system status
- **Metrics**: Monitor Prometheus metrics for performance insights

### Getting Help
- **Issues**: Report bugs and feature requests via GitHub issues
- **Documentation**: Refer to inline code documentation
- **Monitoring**: Use health check and metrics endpoints for diagnostics

---

## 📄 License

This project is proprietary software developed for Kylas CRM platform.

---

**Built with ❤️ by the Kylas AI Team**
