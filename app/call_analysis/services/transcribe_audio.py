import google.generativeai as genai
import os
from loguru import logger
from app.exceptions import ExternalAIModelException
import re

# Configure the API key
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

def transcribe_audio(file_path: str, client_prompt: str) -> str:
    """
    Transcribes the audio file using Google's Gemini API.
    Args:
        file_path (str): The path to the audio file.
    Returns:
        str: The transcription of the audio.
    """
    audio_file = None

    try:
        # Upload the audio file
        audio_file = genai.upload_file(path=file_path, display_name="Audio Clip")

        # Create a model instance
        model = genai.GenerativeModel(model_name="gemini-2.0-flash")

        # Define the prompt for transcription
        prompt = f"""You are a helpful assistant that transcribe customer service call. 
        {client_prompt}
        If the audio is in a language other than English then transcribe it and Then provide an English translation in the transcript

        Format the transcription as a conversation in the following style:
        Format example:
        00:00 Speaker A: Hello, how are you?
        00:03 Speaker B: I'm doing great, thanks for asking!

        If you are not able to find actual names of users then use <PERSON> A, <PERSON> B, etc., to identify speakers.
        "Transcribe the audio clip in the format mentioned above and present it as HTML content. For the transcript, wrap the content in appropriate HTML tags and highlight speaker names using <strong> tags (e.g., <div><strong>00:00 Speaker A:</strong> Hello, how are you?<br><strong>00:03 Speaker B:</strong> I'm doing great, thanks for asking!</div>).\n"
            
        Example response format:
        {{
            "<div><strong>00:00 Speaker A:</strong> Hello, how are you?<br><strong>00:03 Speaker B:</strong> I'm doing great, thanks for asking!</div>",
        }}
        """
        # Generate transcription
        response = model.generate_content([prompt, audio_file])

        # Logs the transcription
        logger.info("------------------- Transcription ----------------")
        logger.info(f"transcription response : {response}")

        return response

    except Exception as e:
        logger.info(f"Call Analysis : An error occurred while genrating transcription: {e}")
        raise ExternalAIModelException(message=str(e))

    finally:
        # Clean up: Delete the uploaded file
        if audio_file:
            genai.delete_file(audio_file.name)
