from openai import OpenAI
import os
import json
import re
from loguru import logger
from app.exceptions import ExternalAIModelException

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def analyze_with_openai(prompt: str) -> tuple[list, dict]:
    """
    Analyzes the note content using OpenAI API.
    Args:
        note_content (str): The content of the note to analyze.
        current_date_time_utc (str): Current UTC timestamp for default due dates
    Returns:
        tuple[list, dict]: Action items list and usage metadata
    """
    try:
        logger.info("Note Analysis : Inside analyze_with_openai function")

        logger.info("Note Analysis : Generating prompt")
        prompt = prompt
        logger.info("Note Analysis : Prompt generated successfully")

        # Generate analysis
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts action items from notes."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1000
        )

        logger.info(f"Note Analysis : OpenAI Response : {response}")

        # Parse the JSON response directly
        try:
            content = response.choices[0].message.content
            action_items = extract_action_items_from_openai_response(content)
            logger.info(f"Note Analysis : Action Items : {action_items}")
        except Exception as e:
            logger.error(f"Note Analysis : Failed to extract action items: {e}")
            raise ExternalAIModelException(message="Invalid response format from OpenAI")

        # Create usage metadata
        usage_metadata = {
            "prompt_token_count": response.usage.prompt_tokens,
            "candidates_token_count": response.usage.completion_tokens,
            "total_token_count": response.usage.total_tokens
        }

        return action_items, usage_metadata

    except Exception as e:
        logger.error(f"Note Analysis : Unexpected error occurred while analyzing note with OpenAI: {e}")
        raise ExternalAIModelException(message=str(e))

def extract_action_items_from_openai_response(content):
    # Try to extract JSON from a code block first
    codeblock_match = re.search(r'```json\s*([\s\S]+?)\s*```', content)
    if codeblock_match:
        json_str = codeblock_match.group(1)
    else:
        # Fallback: try to find the first {...} block
        brace_match = re.search(r'({[\s\S]+})', content)
        if brace_match:
            json_str = brace_match.group(1)
        else:
            json_str = None

    if json_str:
        try:
            data = json.loads(json_str)
            return data.get("actionItems", [])
        except Exception as e:
            logger.error(f"Failed to parse action items JSON: {e}")
            return []
    else:
        logger.error("No JSON found in OpenAI response.")
        return [] 